# utils/error_handler.py
import traceback
import logging
import functools
from typing import Callable, Any, Optional, Union
from .logger import LoggerManager

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def handle_exception(self, exception: Exception, context: str = "") -> None:
        """处理异常"""
        error_msg = f"{context}: {str(exception)}" if context else str(exception)
        self.logger.error(error_msg)
        self.logger.error(f"异常堆栈: {traceback.format_exc()}")
    
    def safe_execute(self, func: Callable, *args, default_return=None, **kwargs) -> Any:
        """安全执行函数"""
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.handle_exception(e, f"执行函数 {func.__name__} 时出错")
            return default_return
    
    def log_and_ignore(self, exception: Exception, context: str = "") -> None:
        """记录异常但忽略（不重新抛出）"""
        self.handle_exception(exception, context)
    
    def log_and_reraise(self, exception: Exception, context: str = "") -> None:
        """记录异常并重新抛出"""
        self.handle_exception(exception, context)
        raise exception

def exception_handler(
    logger_name: str = 'default', 
    default_return: Any = None, 
    re_raise: bool = False,
    log_args: bool = False,
    log_success: bool = False
):
    """
    异常处理装饰器
    
    Args:
        logger_name: 日志器名称
        default_return: 异常时的默认返回值
        re_raise: 是否重新抛出异常
        log_args: 是否记录函数参数
        log_success: 是否记录成功执行
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取日志器
            try:
                logger = LoggerManager().get_logger(logger_name)
            except:
                logger = logging.getLogger(logger_name)
            
            # 记录函数调用
            if log_args:
                # 避免记录self参数中的敏感信息
                safe_args = args[1:] if args and hasattr(args[0], '__class__') else args
                logger.debug(f"调用函数 {func.__name__}, 参数: args={safe_args}, kwargs={kwargs}")
            
            try:
                result = func(*args, **kwargs)
                
                if log_success:
                    logger.debug(f"函数 {func.__name__} 执行成功")
                
                return result
                
            except Exception as e:
                logger.error(f"函数 {func.__name__} 执行异常: {e}")
                logger.debug(f"异常详情: {traceback.format_exc()}")
                
                if re_raise:
                    raise
                
                return default_return
        
        return wrapper
    return decorator

def retry_on_exception(
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,),
    logger_name: str = 'default'
):
    """
    异常重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff_factor: 延迟时间倍增因子
        exceptions: 需要重试的异常类型
        logger_name: 日志器名称
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取日志器
            try:
                logger = LoggerManager().get_logger(logger_name)
            except:
                logger = logging.getLogger(logger_name)
            
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                    
                except exceptions as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}")
                        logger.info(f"将在 {current_delay:.1f} 秒后重试...")
                        
                        import time
                        time.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        logger.error(f"函数 {func.__name__} 在 {max_retries + 1} 次尝试后仍然失败")
                        break
                        
                except Exception as e:
                    # 对于不在重试列表中的异常，直接抛出
                    logger.error(f"函数 {func.__name__} 发生不可重试的异常: {e}")
                    raise
            
            # 重试耗尽，抛出最后一个异常
            if last_exception:
                raise last_exception
        
        return wrapper
    return decorator

def timeout_handler(timeout_seconds: float, logger_name: str = 'default'):
    """
    超时处理装饰器
    
    Args:
        timeout_seconds: 超时时间（秒）
        logger_name: 日志器名称
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import signal
            import threading
            
            # 获取日志器
            try:
                logger = LoggerManager().get_logger(logger_name)
            except:
                logger = logging.getLogger(logger_name)
            
            # 检查是否在主线程中
            if threading.current_thread() is threading.main_thread():
                # 在主线程中使用signal
                def timeout_handler_func(signum, frame):
                    raise TimeoutError(f"函数 {func.__name__} 执行超时 ({timeout_seconds}秒)")
                
                # 设置超时信号
                old_handler = signal.signal(signal.SIGALRM, timeout_handler_func)
                signal.alarm(int(timeout_seconds))
                
                try:
                    result = func(*args, **kwargs)
                    signal.alarm(0)  # 取消超时
                    return result
                except TimeoutError as e:
                    logger.error(str(e))
                    raise
                finally:
                    signal.signal(signal.SIGALRM, old_handler)
            else:
                # 在子线程中使用threading.Timer
                result = [None]
                exception = [None]
                
                def target():
                    try:
                        result[0] = func(*args, **kwargs)
                    except Exception as e:
                        exception[0] = e
                
                thread = threading.Thread(target=target)
                thread.daemon = True
                thread.start()
                thread.join(timeout_seconds)
                
                if thread.is_alive():
                    logger.error(f"函数 {func.__name__} 执行超时 ({timeout_seconds}秒)")
                    raise TimeoutError(f"函数 {func.__name__} 执行超时")
                
                if exception[0]:
                    raise exception[0]
                
                return result[0]
        
        return wrapper
    return decorator

def log_performance(logger_name: str = 'default', log_level: int = logging.INFO):
    """
    性能监控装饰器
    
    Args:
        logger_name: 日志器名称
        log_level: 日志级别
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import time
            
            # 获取日志器
            try:
                logger = LoggerManager().get_logger(logger_name)
            except:
                logger = logging.getLogger(logger_name)
            
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                logger.log(log_level, f"函数 {func.__name__} 执行时间: {execution_time:.3f}秒")
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"函数 {func.__name__} 执行失败 (耗时: {execution_time:.3f}秒): {e}")
                raise
        
        return wrapper
    return decorator

def validate_arguments(**validators):
    """
    参数验证装饰器
    
    Args:
        **validators: 参数验证器字典，格式为 {参数名: 验证函数}
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import inspect
            
            # 获取函数签名
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # 验证参数
            for param_name, validator in validators.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    try:
                        if not validator(value):
                            raise ValueError(f"参数 {param_name} 验证失败: {value}")
                    except Exception as e:
                        raise ValueError(f"参数 {param_name} 验证异常: {e}")
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator

# 常用的验证器函数
class Validators:
    """常用验证器"""
    
    @staticmethod
    def not_none(value):
        """验证值不为None"""
        return value is not None
    
    @staticmethod
    def not_empty(value):
        """验证值不为空（字符串、列表等）"""
        return value is not None and len(value) > 0
    
    @staticmethod
    def positive_number(value):
        """验证正数"""
        return isinstance(value, (int, float)) and value > 0
    
    @staticmethod
    def non_negative_number(value):
        """验证非负数"""
        return isinstance(value, (int, float)) and value >= 0
    
    @staticmethod
    def in_range(min_val, max_val):
        """验证数值在指定范围内"""
        def validator(value):
            return isinstance(value, (int, float)) and min_val <= value <= max_val
        return validator
    
    @staticmethod
    def instance_of(cls):
        """验证实例类型"""
        def validator(value):
            return isinstance(value, cls)
        return validator
    
    @staticmethod
    def file_exists(value):
        """验证文件存在"""
        import os
        return isinstance(value, str) and os.path.isfile(value)
    
    @staticmethod
    def directory_exists(value):
        """验证目录存在"""
        import os
        return isinstance(value, str) and os.path.isdir(value)

# 上下文管理器用于异常处理
class ExceptionContext:
    """异常处理上下文管理器"""
    
    def __init__(self, logger_name: str = 'default', re_raise: bool = True, default_return: Any = None):
        self.logger_name = logger_name
        self.re_raise = re_raise
        self.default_return = default_return
        self.logger = None
        self.exception_occurred = False
        self.result = None
    
    def __enter__(self):
        try:
            self.logger = LoggerManager().get_logger(self.logger_name)
        except:
            self.logger = logging.getLogger(self.logger_name)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.exception_occurred = True
            self.logger.error(f"上下文中发生异常: {exc_val}")
            self.logger.debug(f"异常详情: {traceback.format_exc()}")
            
            if not self.re_raise:
                self.result = self.default_return
                return True  # 抑制异常
        
        return False  # 不抑制异常
    
    def get_result(self):
        """获取结果（当异常被抑制时）"""
        return self.result if self.exception_occurred else None

# 使用示例：
"""
# 1. 基本异常处理
@exception_handler('camera', default_return=False)
def some_function():
    pass

# 2. 重试机制
@retry_on_exception(max_retries=3, delay=1.0)
def unreliable_function():
    pass

# 3. 超时控制
@timeout_handler(timeout_seconds=10.0)
def slow_function():
    pass

# 4. 性能监控
@log_performance('performance')
def monitored_function():
    pass

# 5. 参数验证
@validate_arguments(
    value=Validators.positive_number,
    path=Validators.file_exists
)
def validated_function(value, path):
    pass

# 6. 上下文管理器
with ExceptionContext('context', re_raise=False, default_return=None) as ctx:
    # 一些可能出错的代码
    result = risky_operation()
    
if ctx.exception_occurred:
    result = ctx.get_result()
"""
