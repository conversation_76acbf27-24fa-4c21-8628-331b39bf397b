# utils/logger.py
import logging
import logging.handlers
import os
from datetime import datetime
from typing import Optional

class LoggerManager:
    """日志管理器"""
    
    _instance = None
    _loggers = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.initialized = True
            self.config = None
    
    def setup_logger(self, name: str, config: dict) -> logging.Logger:
        """设置日志器"""
        if name in self._loggers:
            return self._loggers[name]
        
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, config.get('level', 'INFO')))
        
        # 避免重复添加处理器
        if logger.handlers:
            logger.handlers.clear()
        
        # 文件处理器
        log_dir = config.get('log_dir', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            filename=config.get('file_path', f'{log_dir}/app.log'),
            maxBytes=config.get('max_file_size', 10485760),
            backupCount=config.get('backup_count', 5),
            encoding='utf-8'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        self._loggers[name] = logger
        return logger
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取日志器"""
        return self._loggers.get(name, logging.getLogger(name))

# 装饰器用于方法调用日志
def log_method_call(logger_name: str = 'default'):
    """方法调用日志装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = LoggerManager().get_logger(logger_name)
            logger.debug(f"调用方法: {func.__name__}, 参数: args={args[1:] if args else []}, kwargs={kwargs}")
            try:
                result = func(*args, **kwargs)
                logger.debug(f"方法 {func.__name__} 执行成功")
                return result
            except Exception as e:
                logger.error(f"方法 {func.__name__} 执行失败: {e}")
                raise
        return wrapper
    return decorator
