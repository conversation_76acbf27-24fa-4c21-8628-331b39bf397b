# camera/camera_manager.py
import threading
import queue
import time
from typing import Optional, Callable
from dataclasses import dataclass
from ctypes import *
import numpy as np
import cv2

# 导入相机控制模块
from MvImport.MvCameraControl_class import *
from MvImport.CameraParams_header import *
from utils.exceptions import *
from utils.error_handler import exception_handler, ErrorHandler
from utils.logger import LoggerManager

@dataclass
class CameraConfig:
    """相机配置"""
    trigger_timeout: int = 1000
    pixel_format: str = "RGB8"
    exposure_time: int = 10000
    gain: float = 1.0
    frame_rate: float = 30.0

@dataclass
class FrameData:
    """帧数据"""
    image: np.ndarray
    timestamp: float
    frame_id: int
    metadata: dict

class CameraManager:
    """线程安全的相机管理器"""
    
    def __init__(self, config: CameraConfig, logger_name: str = 'camera'):
        self.config = config
        self.logger = LoggerManager().get_logger(logger_name)
        self.error_handler = <PERSON>rror<PERSON>andler(self.logger)
        
        # 相机相关
        self.cam = None
        self.is_initialized = False
        self.is_grabbing = False
        
        # 线程安全
        self._lock = threading.RLock()
        self._capture_thread = None
        self._stop_event = threading.Event()
        
        # 帧缓冲
        self._frame_queue = queue.Queue(maxsize=10)
        self._frame_counter = 0
        
        # 回调函数
        self._frame_callbacks = []
        
        # 性能监控
        self._fps_counter = 0
        self._last_fps_time = time.time()
        self._current_fps = 0.0
    
    @exception_handler('camera', default_return=False)
    def initialize(self) -> bool:
        """初始化相机"""
        with self._lock:
            if self.is_initialized:
                self.logger.warning("相机已经初始化")
                return True
            
            self.logger.info("开始初始化相机")
            
            # 初始化SDK
            ret = MvCamera.MV_CC_Initialize()
            if ret != 0:
                raise CameraInitException(f"初始化SDK失败! ret[0x{ret:x}]")
            
            # 枚举设备
            deviceList = MV_CC_DEVICE_INFO_LIST()
            tlayerType = MV_GIGE_DEVICE | MV_USB_DEVICE
            ret = MvCamera.MV_CC_EnumDevices(tlayerType, deviceList)
            if ret != 0:
                raise CameraInitException(f"枚举设备失败! ret[0x{ret:x}]")
            
            if deviceList.nDeviceNum < 1:
                raise CameraInitException("未找到相机设备")
            
            self.logger.info(f"找到 {deviceList.nDeviceNum} 个相机设备")
            
            # 创建相机句柄
            self.cam = MvCamera()
            stDeviceList = cast(deviceList.pDeviceInfo[0], POINTER(MV_CC_DEVICE_INFO)).contents
            ret = self.cam.MV_CC_CreateHandle(stDeviceList)
            if ret != 0:
                raise CameraInitException(f"创建相机句柄失败! ret[0x{ret:x}]")
            
            # 打开设备
            ret = self.cam.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
            if ret != 0:
                raise CameraInitException(f"打开相机设备失败! ret[0x{ret:x}]")
            
            # 配置相机参数
            self._configure_camera()
            
            self.is_initialized = True
            self.logger.info("相机初始化成功")
            return True
    
    def _configure_camera(self):
        """配置相机参数"""
        try:
            # 设置触发模式
            self.cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
            
            # 设置曝光时间
            self.cam.MV_CC_SetFloatValue("ExposureTime", float(self.config.exposure_time))
            
            # 设置增益
            self.cam.MV_CC_SetFloatValue("Gain", self.config.gain)
            
            # 设置帧率
            self.cam.MV_CC_SetFloatValue("AcquisitionFrameRate", self.config.frame_rate)
            
            self.logger.info("相机参数配置完成")
            
        except Exception as e:
            self.logger.warning(f"配置相机参数时出现警告: {e}")
    
    @exception_handler('camera', default_return=False)
    def start_grabbing(self) -> bool:
        """开始抓取"""
        with self._lock:
            if not self.is_initialized:
                raise CameraOperationException("相机未初始化")
            
            if self.is_grabbing:
                self.logger.warning("相机已在抓取中")
                return True
            
            ret = self.cam.MV_CC_StartGrabbing()
            if ret != 0:
                raise CameraOperationException(f"开始抓取失败! ret[0x{ret:x}]")
            
            self.is_grabbing = True
            self._stop_event.clear()
            
            # 启动抓取线程
            self._capture_thread = threading.Thread(
                target=self._capture_loop,
                name="CameraCapture",
                daemon=True
            )
            self._capture_thread.start()
            
            self.logger.info("开始图像抓取")
            return True
    
    @exception_handler('camera', default_return=False)
    def stop_grabbing(self) -> bool:
        """停止抓取"""
        with self._lock:
            if not self.is_grabbing:
                return True
            
            self.logger.info("停止图像抓取")
            self._stop_event.set()
            
            # 等待抓取线程结束
            if self._capture_thread and self._capture_thread.is_alive():
                self._capture_thread.join(timeout=2.0)
                if self._capture_thread.is_alive():
                    self.logger.warning("抓取线程未能正常结束")
            
            if self.cam:
                ret = self.cam.MV_CC_StopGrabbing()
                if ret != 0:
                    self.logger.error(f"停止抓取失败! ret[0x{ret:x}]")
                    return False
            
            self.is_grabbing = False
            self.logger.info("图像抓取已停止")
            return True
    
    def _capture_loop(self):
        """抓取循环"""
        self.logger.info("抓取线程启动")
        stOutFrame = MV_FRAME_OUT()
        
        while not self._stop_event.is_set():
            try:
                memset(byref(stOutFrame), 0, sizeof(stOutFrame))
                
                ret = self.cam.MV_CC_GetImageBuffer(stOutFrame, self.config.trigger_timeout)
                if ret != 0:
                    if ret == MV_E_TRIGGER_TIMEOUT:
                        continue
                    self.logger.error(f"获取图像失败! ret[0x{ret:x}]")
                    continue
                
                # 处理图像
                frame_data = self._process_frame(stOutFrame)
                if frame_data:
                    # 添加到队列
                    try:
                        self._frame_queue.put_nowait(frame_data)
                    except queue.Full:
                        # 队列满时丢弃最旧的帧
                        try:
                            self._frame_queue.get_nowait()
                            self._frame_queue.put_nowait(frame_data)
                        except queue.Empty:
                            pass
                    
                    # 调用回调函数
                    self._call_frame_callbacks(frame_data)
                    
                    # 更新FPS
                    self._update_fps()
                
            except Exception as e:
                self.logger.error(f"抓取循环异常: {e}")
            finally:
                if stOutFrame.pBufAddr:
                    self.cam.MV_CC_FreeImageBuffer(stOutFrame)
        
        self.logger.info("抓取线程结束")
    
    def _process_frame(self, stOutFrame) -> Optional[FrameData]:
        """处理帧数据"""
        try:
            if not stOutFrame.pBufAddr or stOutFrame.stFrameInfo.nFrameLen == 0:
                return None
            
            # 转换图像数据
            pData = cast(stOutFrame.pBufAddr, POINTER(c_ubyte * stOutFrame.stFrameInfo.nFrameLen))
            np_data = np.frombuffer(pData.contents, dtype=np.uint8)
            
            nWidth = stOutFrame.stFrameInfo.nWidth
            nHeight = stOutFrame.stFrameInfo.nHeight
            pixel_format = stOutFrame.stFrameInfo.enPixelType
            
            image = self._convert_pixel_format(np_data, nWidth, nHeight, pixel_format)
            if image is None:
                return None
            
            self._frame_counter += 1
            
            return FrameData(
                image=image,
                timestamp=time.time(),
                frame_id=self._frame_counter,
                metadata={
                    'width': nWidth,
                    'height': nHeight,
                    'pixel_format': pixel_format
                }
            )
            
        except Exception as e:
            self.logger.error(f"处理帧数据失败: {e}")
            return None
    
    def _convert_pixel_format(self, np_data: np.ndarray, width: int, height: int, pixel_format) -> Optional[np.ndarray]:
        """转换像素格式"""
        try:
            if pixel_format == PixelType_Gvsp_BGR8_Packed:
                image = np_data.reshape(height, width, 3)
                return cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            elif pixel_format == PixelType_Gvsp_RGB8_Packed:
                return np_data.reshape(height, width, 3)
            elif pixel_format in [PixelType_Gvsp_BayerRG8, PixelType_Gvsp_BayerGB8,
                                  PixelType_Gvsp_BayerGR8, PixelType_Gvsp_BayerBG8]:
                image = np_data.reshape(height, width)
                return cv2.cvtColor(image, cv2.COLOR_BAYER_RG2RGB)
            else:
                self.logger.error(f"不支持的像素格式: {pixel_format}")
                return None
        except Exception as e:
            self.logger.error(f"像素格式转换失败: {e}")
            return None
    
    def _update_fps(self):
        """更新FPS"""
        self._fps_counter += 1
        current_time = time.time()
        if current_time - self._last_fps_time >= 1.0:
            self._current_fps = self._fps_counter / (current_time - self._last_fps_time)
            self._fps_counter = 0
            self._last_fps_time = current_time
    
    def get_latest_frame(self, timeout: float = 0.1) -> Optional[FrameData]:
        """获取最新帧"""
        try:
            return self._frame_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def add_frame_callback(self, callback: Callable[[FrameData], None]):
        """添加帧回调"""
        self._frame_callbacks.append(callback)
    
    def remove_frame_callback(self, callback: Callable[[FrameData], None]):
        """移除帧回调"""
        if callback in self._frame_callbacks:
            self._frame_callbacks.remove(callback)
    
    def _call_frame_callbacks(self, frame_data: FrameData):
        """调用帧回调"""
        for callback in self._frame_callbacks:
            try:
                callback(frame_data)
            except Exception as e:
                self.logger.error(f"帧回调执行失败: {e}")
    
    def get_fps(self) -> float:
        """获取当前FPS"""
        return self._current_fps
    
    def set_trigger_mode(self, mode: str) -> bool:
        """设置触发模式"""
        with self._lock:
            if not self.is_initialized:
                return False
            
            try:
                if mode == "Off":
                    ret = self.cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
                elif mode == "Software":
                    ret = self.cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_ON)
                    ret = self.cam.MV_CC_SetEnumValue("TriggerSource", MV_TRIGGER_SOURCE_SOFTWARE)
                elif mode == "Hardware":
                    ret = self.cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_ON)
                    ret = self.cam.MV_CC_SetEnumValue("TriggerSource", MV_TRIGGER_SOURCE_LINE0)
                else:
                    return False
                
                if ret == 0:
                    self.logger.info(f"触发模式设置为: {mode}")
                    return True
                else:
                    self.logger.error(f"设置触发模式失败: 0x{ret:x}")
                    return False
                    
            except Exception as e:
                self.logger.error(f"设置触发模式异常: {e}")
                return False
    
    def software_trigger(self) -> bool:
        """软件触发"""
        with self._lock:
            if not self.is_initialized:
                return False
            
            try:
                ret = self.cam.MV_CC_SetCommandValue("TriggerSoftware")
                if ret == 0:
                    self.logger.debug("软件触发执行成功")
                    return True
                else:
                    self.logger.error(f"软件触发失败: 0x{ret:x}")
                    return False
            except Exception as e:
                self.logger.error(f"软件触发异常: {e}")
                return False
    
    @exception_handler('camera')
    def cleanup(self):
        """清理资源"""
        self.logger.info("开始清理相机资源")
        
        self.stop_grabbing()
        
        with self._lock:
            if self.cam:
                try:
                    self.cam.MV_CC_CloseDevice()
                    self.cam.MV_CC_DestroyHandle()
                except Exception as e:
                    self.logger.error(f"关闭相机设备失败: {e}")
                
                self.cam = None
            
            if self.is_initialized:
                try:
                    MvCamera.MV_CC_Finalize()
                except Exception as e:
                    self.logger.error(f"SDK清理失败: {e}")
                
                self.is_initialized = False
        
        self.logger.info("相机资源清理完成")
