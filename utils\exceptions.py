# utils/exceptions.py
class CameraSystemException(Exception):
    """相机系统基础异常"""
    pass

class CameraInitException(CameraSystemException):
    """相机初始化异常"""
    pass

class CameraOperationException(CameraSystemException):
    """相机操作异常"""
    pass

class DetectionException(CameraSystemException):
    """检测异常"""
    pass

class NetworkException(CameraSystemException):
    """网络异常"""
    pass

class ConfigException(CameraSystemException):
    """配置异常"""
    pass

# utils/error_handler.py
import traceback
import logging
from typing import Callable, Any
from functools import wraps

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def handle_exception(self, exception: Exception, context: str = "") -> None:
        """处理异常"""
        error_msg = f"{context}: {str(exception)}" if context else str(exception)
        self.logger.error(error_msg)
        self.logger.error(f"异常堆栈: {traceback.format_exc()}")
    
    def safe_execute(self, func: Callable, *args, default_return=None, **kwargs) -> Any:
        """安全执行函数"""
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.handle_exception(e, f"执行函数 {func.__name__} 时出错")
            return default_return

def exception_handler(logger_name: str = 'default', default_return=None, re_raise: bool = False):
    """异常处理装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            logger = LoggerManager().get_logger(logger_name)
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"函数 {func.__name__} 执行异常: {e}")
                logger.error(f"异常堆栈: {traceback.format_exc()}")
                if re_raise:
                    raise
                return default_return
        return wrapper
    return decorator
