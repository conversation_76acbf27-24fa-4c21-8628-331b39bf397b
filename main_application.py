# main_application.py
import sys
import os
import time
from datetime import datetime
from typing import Optional, Dict, Any
import cv2
import numpy as np
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *


sys.path.append("../MvImport")

# 导入自定义模块
from config.config_manager import ConfigManager
from utils.logger import LoggerManager
from utils.exceptions import *
from utils.error_handler import exception_handler
from camera.camera_manager import CameraManager, CameraConfig, FrameData
from detection.detection_processor import DetectionProcessor, DetectionConfig, DetectionResult
from network.server_manager import ServerManager, ServerConfig
from ui.main_window import MainWindow

class ApplicationController:
    """应用程序控制器"""
    
    def __init__(self):
        # 配置管理
        self.config_manager = ConfigManager()
        
        # 日志管理
        self.logger_manager = LoggerManager()
        self.logger = self.logger_manager.setup_logger(
            'main',
            self.config_manager.get('logging')
        )
        
        # 核心组件
        self.camera_manager = None
        self.detection_processor = None
        self.server_manager = None
        self.main_window = None
        
        # 状态管理
        self.is_initialized = False
        self.is_running = False
        
        self.logger.info("应用程序控制器初始化完成")
    
    @exception_handler('main', default_return=False)
    def initialize(self) -> bool:
        """初始化应用程序"""
        self.logger.info("开始初始化应用程序")
        
        try:
            # 创建必要的目录
            self._create_directories()
            
            # 初始化相机管理器
            camera_config = CameraConfig(
                trigger_timeout=self.config_manager.get('camera.trigger_timeout'),
                pixel_format=self.config_manager.get('camera.pixel_format'),
                exposure_time=self.config_manager.get('camera.exposure_time')
            )
            self.camera_manager = CameraManager(camera_config, 'camera')
            
            # 初始化检测处理器
            detection_config = DetectionConfig(
                model_path=self.config_manager.get('detection.model_path'),
                confidence_threshold=self.config_manager.get('detection.confidence_threshold'),
                iou_threshold=self.config_manager.get('detection.iou_threshold'),
                max_detections=self.config_manager.get('detection.max_detections')
            )
            self.detection_processor = DetectionProcessor(detection_config, 'detection')
            
            # 初始化网络服务器
            server_config = ServerConfig(
                host=self.config_manager.get('server.host'),
                port=self.config_manager.get('server.port'),
                max_connections=self.config_manager.get('server.max_connections'),
                timeout=self.config_manager.get('server.timeout')
            )
            self.server_manager = ServerManager(server_config, 'server')
            
            # 初始化组件
            if not self.camera_manager.initialize():
                raise CameraInitException("相机初始化失败")
            
            if not self.detection_processor.initialize():
                raise DetectionException("检测器初始化失败")
            
            # 设置回调函数
            self._setup_callbacks()
            
            self.is_initialized = True
            self.logger.info("应用程序初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"应用程序初始化失败: {e}")
            return False
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self.config_manager.get('paths.output_dir'),
            self.config_manager.get('paths.log_dir'),
            self.config_manager.get('paths.config_dir')
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            self.logger.debug(f"创建目录: {directory}")
    
    def _setup_callbacks(self):
        """设置回调函数"""
        # 相机帧回调
        self.camera_manager.add_frame_callback(self._on_new_frame)
        
        # 检测结果回调
        self.detection_processor.add_result_callback(self._on_detection_result)
    
    def _on_new_frame(self, frame_data: FrameData):
        """新帧回调"""
        # 发送到检测处理器
        self.detection_processor.process_frame(frame_data)
    
    def _on_detection_result(self, result: DetectionResult):
        """检测结果回调"""
        # 发送到网络服务器
        if self.server_manager and self.server_manager.is_running:
            self.server_manager.send_detection_result(result)
        
        # 发送到UI
        if self.main_window:
            self.main_window.update_detection_result(result)
    
    def start_capture(self) -> bool:
        """开始采集"""
        if not self.is_initialized:
            self.logger.error("应用程序未初始化")
            return False
        
        try:
            # 启动检测处理
            self.detection_processor.start_processing()
            
            # 启动相机采集
            if not self.camera_manager.start_grabbing():
                self.detection_processor.stop_processing()
                return False
            
            self.is_running = True
            self.logger.info("开始采集")
            return True
            
        except Exception as e:
            self.logger.error(f"启动采集失败: {e}")
            return False
    
    def stop_capture(self) -> bool:
        """停止采集"""
        if not self.is_running:
            return True
        
        try:
            # 停止相机采集
            self.camera_manager.stop_grabbing()
            
            # 停止检测处理
            self.detection_processor.stop_processing()
            
            self.is_running = False
            self.logger.info("停止采集")
            return True
            
        except Exception as e:
            self.logger.error(f"停止采集失败: {e}")
            return False
    
    def start_server(self) -> bool:
        """启动服务器"""
        try:
            return self.server_manager.start()
        except Exception as e:
            self.logger.error(f"启动服务器失败: {e}")
            return False
    
    def stop_server(self):
        """停止服务器"""
        if self.server_manager:
            self.server_manager.stop()
    
    def set_trigger_mode(self, mode: str) -> bool:
        """设置触发模式"""
        if self.camera_manager:
            return self.camera_manager.set_trigger_mode(mode)
        return False
    
    def software_trigger(self) -> bool:
        """软件触发"""
        if self.camera_manager:
            return self.camera_manager.software_trigger()
        return False
    
    def save_current_image(self, image: np.ndarray) -> bool:
        """保存当前图像"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            output_dir = self.config_manager.get('paths.output_dir')
            file_path = os.path.join(output_dir, f"capture_{timestamp}.png")
            
            # 转换颜色空间
            if len(image.shape) == 3 and image.shape[2] == 3:
                bgr_image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            else:
                bgr_image = image
            
            success = cv2.imwrite(file_path, bgr_image)
            if success:
                self.logger.info(f"图像保存成功: {file_path}")
                return True
            else:
                self.logger.error(f"图像保存失败: {file_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"保存图像异常: {e}")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        status = {
            'timestamp': time.time(),
            'is_initialized': self.is_initialized,
            'is_running': self.is_running,
            'camera': {
                'is_initialized': self.camera_manager.is_initialized if self.camera_manager else False,
                'is_grabbing': self.camera_manager.is_grabbing if self.camera_manager else False,
                'fps': self.camera_manager.get_fps() if self.camera_manager else 0.0
            },
            'detection': {
                'is_loaded': self.detection_processor.is_loaded if self.detection_processor else False,
                'stats': self.detection_processor.get_stats() if self.detection_processor else {}
            },
            'server': {
                'is_running': self.server_manager.is_running if self.server_manager else False,
                'client_count': self.server_manager.get_client_count() if self.server_manager else 0
            }
        }
        return status
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("开始清理应用程序资源")
        
        # 停止采集
        self.stop_capture()
        
        # 停止服务器
        self.stop_server()
        
        # 清理组件
        if self.detection_processor:
            self.detection_processor.cleanup()
        
        if self.camera_manager:
            self.camera_manager.cleanup()
        
        # 保存配置
        self.config_manager.save_config()
        
        self.logger.info("应用程序资源清理完成")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 创建应用程序控制器
        controller = ApplicationController()
        
        # 初始化
        if not controller.initialize():
            QMessageBox.critical(None, "错误", "应用程序初始化失败")
            return 1
        
        # 创建主窗口
        controller.main_window = MainWindow(controller)
        controller.main_window.show()
        
        # 运行应用程序
        result = app.exec_()
        
        # 清理资源
        controller.cleanup()
        
        return result
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
