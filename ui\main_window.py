# ui/main_window.py
import os
from typing import Optional
from datetime import datetime
import numpy as np
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *

from detection.detection_processor import DetectionResult

class StatusWidget(QWidget):
    """状态显示组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        layout = QGridLayout(self)
        
        # 相机状态
        self.camera_status = QLabel("相机: 未连接")
        self.camera_status.setStyleSheet("color: red;")
        layout.addWidget(QLabel("相机状态:"), 0, 0)
        layout.addWidget(self.camera_status, 0, 1)
        
        # FPS显示
        self.fps_label = QLabel("FPS: 0.0")
        layout.addWidget(QLabel("帧率:"), 1, 0)
        layout.addWidget(self.fps_label, 1, 1)
        
        # 检测状态
        self.detection_status = QLabel("检测: 未启动")
        self.detection_status.setStyleSheet("color: red;")
        layout.addWidget(QLabel("检测状态:"), 2, 0)
        layout.addWidget(self.detection_status, 2, 1)
        
        # 服务器状态
        self.server_status = QLabel("服务器: 未启动")
        self.server_status.setStyleSheet("color: red;")
        layout.addWidget(QLabel("服务器状态:"), 3, 0)
        layout.addWidget(self.server_status, 3, 1)
        
        # 客户端连接数
        self.client_count = QLabel("连接数: 0")
        layout.addWidget(QLabel("客户端:"), 4, 0)
        layout.addWidget(self.client_count, 4, 1)
    
    def update_camera_status(self, is_connected: bool, is_grabbing: bool):
        if is_connected:
            if is_grabbing:
                self.camera_status.setText("相机: 采集中")
                self.camera_status.setStyleSheet("color: green;")
            else:
                self.camera_status.setText("相机: 已连接")
                self.camera_status.setStyleSheet("color: orange;")
        else:
            self.camera_status.setText("相机: 未连接")
            self.camera_status.setStyleSheet("color: red;")
    
    def update_fps(self, fps: float):
        self.fps_label.setText(f"FPS: {fps:.1f}")
    
    def update_detection_status(self, is_active: bool):
        if is_active:
            self.detection_status.setText("检测: 运行中")
            self.detection_status.setStyleSheet("color: green;")
        else:
            self.detection_status.setText("检测: 未启动")
            self.detection_status.setStyleSheet("color: red;")
    
    def update_server_status(self, is_running: bool, client_count: int):
        if is_running:
            self.server_status.setText("服务器: 运行中")
            self.server_status.setStyleSheet("color: green;")
        else:
            self.server_status.setText("服务器: 未启动")
            self.server_status.setStyleSheet("color: red;")
        
        self.client_count.setText(f"连接数: {client_count}")

class ImageDisplayWidget(QLabel):
    """图像显示组件"""
    
    image_clicked = pyqtSignal(QPoint)
    
    def __init__(self):
        super().__init__()
        self.setAlignment(Qt.AlignCenter)
        self.setStyleSheet("""
            QLabel {
                background-color: #2b2b2b;
                border: 2px solid #555555;
                border-radius: 5px;
            }
        """)
        self.setMinimumSize(640, 480)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 显示默认文本
        self.setText("等待图像...")
        self.setStyleSheet(self.styleSheet() + "color: #888888; font-size: 16px;")
    
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.image_clicked.emit(event.pos())
        super().mousePressEvent(event)
    
    def update_image(self, image: np.ndarray):
        """更新显示的图像"""
        try:
            h, w, ch = image.shape
            bytes_per_line = ch * w
            q_img = QImage(image.data, w, h, bytes_per_line, QImage.Format_RGB888)
            
            # 缩放图像以适应显示区域
            pixmap = QPixmap.fromImage(q_img)
            scaled_pixmap = pixmap.scaled(
                self.size(),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            
            self.setPixmap(scaled_pixmap)
            
        except Exception as e:
            print(f"更新图像显示失败: {e}")

class ControlPanelWidget(QWidget):
    """控制面板组件"""
    
    # 信号定义
    start_capture_requested = pyqtSignal()
    stop_capture_requested = pyqtSignal()
    save_image_requested = pyqtSignal()
    trigger_mode_changed = pyqtSignal(str)
    software_trigger_requested = pyqtSignal()
    server_toggle_requested = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 触发模式组
        trigger_group = QGroupBox("触发模式")
        trigger_layout = QHBoxLayout(trigger_group)
        
        self.trigger_off = QRadioButton("连续采集")
        self.trigger_off.setChecked(True)
        self.trigger_off.toggled.connect(lambda: self.trigger_mode_changed.emit("Off"))
        
        self.trigger_software = QRadioButton("软件触发")
        self.trigger_software.toggled.connect(lambda: self.trigger_mode_changed.emit("Software"))
        
        self.trigger_hardware = QRadioButton("硬件触发")
        self.trigger_hardware.toggled.connect(lambda: self.trigger_mode_changed.emit("Hardware"))
        
        self.soft_trigger_btn = QPushButton("执行触发")
        self.soft_trigger_btn.setEnabled(False)
        self.soft_trigger_btn.clicked.connect(self.software_trigger_requested.emit)
        
        trigger_layout.addWidget(self.trigger_off)
        trigger_layout.addWidget(self.trigger_software)
        trigger_layout.addWidget(self.trigger_hardware)
        trigger_layout.addWidget(self.soft_trigger_btn)
        
        layout.addWidget(trigger_group)
        
        # 控制按钮组
        control_group = QGroupBox("控制操作")
        control_layout = QGridLayout(control_group)
        
        self.start_btn = QPushButton("开始采集")
        self.start_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.start_btn.clicked.connect(self.start_capture_requested.emit)
        
        self.stop_btn = QPushButton("停止采集")
        self.stop_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_capture_requested.emit)
        
        self.save_btn = QPushButton("保存图像")
        self.save_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        self.save_btn.setEnabled(False)
        self.save_btn.clicked.connect(self.save_image_requested.emit)
        
        self.server_btn = QPushButton("启动服务器")
        self.server_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }")
        self.server_btn.clicked.connect(self.server_toggle_requested.emit)
        
        control_layout.addWidget(self.start_btn, 0, 0)
        control_layout.addWidget(self.stop_btn, 0, 1)
        control_layout.addWidget(self.save_btn, 1, 0)
        control_layout.addWidget(self.server_btn, 1, 1)
        
        layout.addWidget(control_group)
    
    def set_capture_state(self, is_capturing: bool):
        """设置采集状态"""
        self.start_btn.setEnabled(not is_capturing)
        self.stop_btn.setEnabled(is_capturing)
        self.save_btn.setEnabled(is_capturing)
    
    def set_trigger_mode_enabled(self, mode: str):
        """设置触发模式"""
        self.soft_trigger_btn.setEnabled(mode == "Software")
    
    def set_server_state(self, is_running: bool):
        """设置服务器状态"""
        if is_running:
            self.server_btn.setText("停止服务器")
            self.server_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        else:
            self.server_btn.setText("启动服务器")
            self.server_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }")

class DetectionInfoWidget(QWidget):
    """检测信息显示组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 检测统计
        stats_group = QGroupBox("检测统计")
        stats_layout = QGridLayout(stats_group)
        
        self.detection_count = QLabel("检测数量: 0")
        self.processing_time = QLabel("处理时间: 0.000s")
        self.detection_fps = QLabel("检测FPS: 0.0")
        
        stats_layout.addWidget(self.detection_count, 0, 0)
        stats_layout.addWidget(self.processing_time, 0, 1)
        stats_layout.addWidget(self.detection_fps, 1, 0)
        
        layout.addWidget(stats_group)
        
        # 检测结果列表
        results_group = QGroupBox("检测结果")
        results_layout = QVBoxLayout(results_group)
        
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(4)
        self.results_table.setHorizontalHeaderLabels(["类别", "置信度", "中心点", "面积"])
        self.results_table.horizontalHeader().setStretchLastSection(True)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setMaximumHeight(200)
        
        results_layout.addWidget(self.results_table)
        layout.addWidget(results_group)
    
    def update_detection_result(self, result: DetectionResult):
        """更新检测结果"""
        # 更新统计信息
        detection_count = len(result.detections)
        self.detection_count.setText(f"检测数量: {detection_count}")
        self.processing_time.setText(f"处理时间: {result.processing_time:.3f}s")
        
        if result.processing_time > 0:
            fps = 1.0 / result.processing_time
            self.detection_fps.setText(f"检测FPS: {fps:.1f}")
        
        # 更新结果表格
        self.results_table.setRowCount(detection_count)
        
        for i, detection in enumerate(result.detections):
            self.results_table.setItem(i, 0, QTableWidgetItem(detection.label))
            self.results_table.setItem(i, 1, QTableWidgetItem(f"{detection.confidence:.3f}"))
            self.results_table.setItem(i, 2, QTableWidgetItem(f"({detection.center['x']}, {detection.center['y']})"))
            self.results_table.setItem(i, 3, QTableWidgetItem(str(detection.area)))

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self, controller):
        super().__init__()
        self.controller = controller
        self.current_image = None
        
        self.setup_ui()
        self.setup_connections()
        self.setup_timer()
        
        # 设置窗口属性
        self.setWindowTitle("千歌机器人视觉相机采集系统 v2.0")
        self.setWindowIcon(QIcon("resources/icon.png"))  # 如果有图标文件
        
        # 应用样式
        self.apply_style()
    
    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧面板
        left_panel = QVBoxLayout()
        
        # 图像显示
        self.image_display = ImageDisplayWidget()
        left_panel.addWidget(self.image_display, stretch=1)
        
        # 控制面板
        self.control_panel = ControlPanelWidget()
        left_panel.addWidget(self.control_panel)
        
        main_layout.addLayout(left_panel, stretch=2)
        
        # 右侧面板
        right_panel = QVBoxLayout()
        
        # 状态显示
        self.status_widget = StatusWidget()
        right_panel.addWidget(self.status_widget)
        
        # 检测信息
        self.detection_info = DetectionInfoWidget()
        right_panel.addWidget(self.detection_info, stretch=1)
        
        main_layout.addLayout(right_panel, stretch=1)
        
        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
        
        # 菜单栏
        self.create_menu_bar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        save_action = QAction('保存图像', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_image)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图')
        
        fullscreen_action = QAction('全屏', self)
        fullscreen_action.setShortcut('F11')
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_connections(self):
        """设置信号连接"""
        # 控制面板信号
        self.control_panel.start_capture_requested.connect(self.start_capture)
        self.control_panel.stop_capture_requested.connect(self.stop_capture)
        self.control_panel.save_image_requested.connect(self.save_image)
        self.control_panel.trigger_mode_changed.connect(self.set_trigger_mode)
        self.control_panel.software_trigger_requested.connect(self.software_trigger)
        self.control_panel.server_toggle_requested.connect(self.toggle_server)
        
        # 图像显示信号
        self.image_display.image_clicked.connect(self.on_image_clicked)
    
    def setup_timer(self):
        """设置定时器"""
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新一次
    
    def apply_style(self):
        """应用样式"""
        style = """
        QMainWindow {
            background-color: #f0f0f0;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QPushButton {
            padding: 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            opacity: 0.8;
        }
        QPushButton:pressed {
            opacity: 0.6;
        }
        QTableWidget {
            gridline-color: #d0d0d0;
            background-color: white;
        }
        QTableWidget::item {
            padding: 4px;
        }
        """
        self.setStyleSheet(style)
    
    @pyqtSlot()
    def start_capture(self):
        """开始采集"""
        if self.controller.start_capture():
            self.control_panel.set_capture_state(True)
            self.status_bar.showMessage("采集中...")
        else:
            QMessageBox.warning(self, "警告", "启动采集失败")
    
    @pyqtSlot()
    def stop_capture(self):
        """停止采集"""
        if self.controller.stop_capture():
            self.control_panel.set_capture_state(False)
            self.status_bar.showMessage("采集已停止")
        else:
            QMessageBox.warning(self, "警告", "停止采集失败")
    
    @pyqtSlot()
    def save_image(self):
        """保存图像"""
        if self.current_image is not None:
            if self.controller.save_current_image(self.current_image):
                self.status_bar.showMessage("图像保存成功", 3000)
            else:
                QMessageBox.warning(self, "警告", "保存图像失败")
        else:
            QMessageBox.information(self, "提示", "没有可保存的图像")
    
    @pyqtSlot(str)
    def set_trigger_mode(self, mode: str):
        """设置触发模式"""
        if self.controller.set_trigger_mode(mode):
            self.control_panel.set_trigger_mode_enabled(mode)
            self.status_bar.showMessage(f"触发模式已设置为: {mode}", 3000)
        else:
            QMessageBox.warning(self, "警告", "设置触发模式失败")
    
    @pyqtSlot()
    def software_trigger(self):
        """软件触发"""
        if self.controller.software_trigger():
            self.status_bar.showMessage("软件触发执行成功", 2000)
        else:
            QMessageBox.warning(self, "警告", "软件触发失败")
    
    @pyqtSlot()
    def toggle_server(self):
        """切换服务器状态"""
        if self.controller.server_manager.is_running:
            self.controller.stop_server()
            self.control_panel.set_server_state(False)
            self.status_bar.showMessage("服务器已停止", 3000)
        else:
            if self.controller.start_server():
                self.control_panel.set_server_state(True)
                self.status_bar.showMessage("服务器已启动", 3000)
            else:
                QMessageBox.warning(self, "警告", "启动服务器失败")
    
    @pyqtSlot(QPoint)
    def on_image_clicked(self, pos: QPoint):
        """图像点击事件"""
        # 可以在这里添加图像点击处理逻辑
        print(f"图像点击位置: {pos.x()}, {pos.y()}")
    
    def update_detection_result(self, result: DetectionResult):
        """更新检测结果"""
        # 更新图像显示
        self.current_image = result.annotated_image
        self.image_display.update_image(result.annotated_image)
        
        # 更新检测信息
        self.detection_info.update_detection_result(result)
    
    def update_status(self):
        """更新状态显示"""
        try:
            status = self.controller.get_system_status()
            
            # 更新相机状态
            self.status_widget.update_camera_status(
                status['camera']['is_initialized'],
                status['camera']['is_grabbing']
            )
            
            # 更新FPS
            self.status_widget.update_fps(status['camera']['fps'])
            
            # 更新检测状态
            self.status_widget.update_detection_status(
                status['detection']['is_loaded']
            )
            
            # 更新服务器状态
            self.status_widget.update_server_status(
                status['server']['is_running'],
                status['server']['client_count']
            )
            
        except Exception as e:
            print(f"更新状态失败: {e}")
    
    def toggle_fullscreen(self):
        """切换全屏"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
                         "千歌机器人视觉相机采集系统 v2.0\n\n"
                         "基于PyQt5和YOLO的智能视觉系统\n"
                         "支持多种触发模式和网络通信\n\n"
                         "© 2024 千歌机器人")
    
    def closeEvent(self, event):
        """关闭事件"""
        reply = QMessageBox.question(self, '确认退出', 
                                   '确定要退出程序吗？',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.controller.cleanup()
            event.accept()
        else:
            event.ignore()
