# config/config_manager.py
import json
import os
import logging
from typing import Dict, Any

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config/settings.json"):
        self.config_file = config_file
        self.config = self._load_default_config()
        self.load_config()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            "camera": {
                "trigger_timeout": 1000,
                "pixel_format": "RGB8",
                "exposure_time": 10000
            },
            "detection": {
                "model_path": "weights/yolo11n.pt",
                "confidence_threshold": 0.5,
                "iou_threshold": 0.45,
                "max_detections": 100
            },
            "ui": {
                "window_width": 1200,
                "window_height": 800,
                "image_display_width": 640,
                "image_display_height": 480,
                "theme": "default"
            },
            "server": {
                "host": "127.0.0.1",
                "port": 8868,
                "max_connections": 5,
                "timeout": 30
            },
            "logging": {
                "level": "INFO",
                "file_path": "logs/camera_system.log",
                "max_file_size": 10485760,  # 10MB
                "backup_count": 5
            },
            "paths": {
                "output_dir": "captured_images",
                "log_dir": "logs",
                "config_dir": "config"
            },
            "performance": {
                "max_fps": 30,
                "buffer_size": 10,
                "thread_pool_size": 4
            }
        }
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    self._merge_config(self.config, loaded_config)
                return True
            else:
                self.save_config()
                return True
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
            return False
    
    def _merge_config(self, default: dict, loaded: dict):
        """合并配置"""
        for key, value in loaded.items():
            if key in default:
                if isinstance(value, dict) and isinstance(default[key], dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
    
    def get(self, key_path: str, default=None):
        """获取配置值"""
        keys = key_path.split('.')
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value
    
    def set(self, key_path: str, value):
        """设置配置值"""
        keys = key_path.split('.')
        config = self.config
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        config[keys[-1]] = value
