# detection/detection_processor.py
import threading
import queue
import time
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
import numpy as np
import cv2

from ultralytics import YOLO
from utils.exceptions import DetectionException
from utils.error_handler import exception_handler, ErrorHandler
from utils.logger import LoggerManager
from camera.camera_manager import FrameData

@dataclass
class DetectionConfig:
    """检测配置"""
    model_path: str
    confidence_threshold: float = 0.5
    iou_threshold: float = 0.45
    max_detections: int = 100
    input_size: tuple = (640, 640)
    device: str = 'cpu'  # 'cpu' or 'cuda'

@dataclass
class Detection:
    """检测结果"""
    label: str
    confidence: float
    bbox: Dict[str, int]  # x1, y1, x2, y2
    center: Dict[str, int]  # x, y
    area: int
    class_id: int

@dataclass
class DetectionResult:
    """检测结果集"""
    frame_id: int
    timestamp: float
    detections: List[Detection]
    processing_time: float
    annotated_image: np.ndarray

class DetectionProcessor:
    """目标检测处理器"""
    
    def __init__(self, config: DetectionConfig, logger_name: str = 'detection'):
        self.config = config
        self.logger = LoggerManager().get_logger(logger_name)
        self.error_handler = ErrorHandler(self.logger)
        
        # 模型相关
        self.model = None
        self.is_loaded = False
        
        # 线程安全
        self._lock = threading.RLock()
        self._processing_thread = None
        self._stop_event = threading.Event()
        
        # 队列
        self._input_queue = queue.Queue(maxsize=10)
        self._result_queue = queue.Queue(maxsize=10)
        
        # 回调函数
        self._result_callbacks = []
        
        # 性能统计
        self._total_processed = 0
        self._total_processing_time = 0.0
        self._last_stats_time = time.time()
    
    @exception_handler('detection', default_return=False)
    def initialize(self) -> bool:
        """初始化检测器"""
        with self._lock:
            if self.is_loaded:
                self.logger.warning("检测模型已加载")
                return True
            
            self.logger.info(f"加载YOLO模型: {self.config.model_path}")
            
            try:
                self.model = YOLO(self.config.model_path)
                
                # 预热模型
                dummy_input = np.zeros((640, 640, 3), dtype=np.uint8)
                self.model.predict(dummy_input, verbose=False)
                
                self.is_loaded = True
                self.logger.info("YOLO模型加载成功")
                return True
                
            except Exception as e:
                raise DetectionException(f"加载YOLO模型失败: {e}")
    
    def start_processing(self):
        """开始处理"""
        with self._lock:
            if not self.is_loaded:
                raise DetectionException("检测模型未加载")
            
            if self._processing_thread and self._processing_thread.is_alive():
                self.logger.warning("检测处理线程已在运行")
                return
            
            self._stop_event.clear()
            self._processing_thread = threading.Thread(
                target=self._processing_loop,
                name="DetectionProcessor",
                daemon=True
            )
            self._processing_thread.start()
            self.logger.info("开始检测处理")
    
    def stop_processing(self):
        """停止处理"""
        self.logger.info("停止检测处理")
        self._stop_event.set()
        
        if self._processing_thread and self._processing_thread.is_alive():
            self._processing_thread.join(timeout=2.0)
            if self._processing_thread.is_alive():
                self.logger.warning("检测处理线程未能正常结束")
    
    def _processing_loop(self):
        """处理循环"""
        self.logger.info("检测处理线程启动")
        
        while not self._stop_event.is_set():
            try:
                # 获取输入帧
                frame_data = self._input_queue.get(timeout=0.1)
                
                # 处理检测
                result = self._process_detection(frame_data)
                if result:
                    # 添加到结果队列
                    try:
                        self._result_queue.put_nowait(result)
                    except queue.Full:
                        # 队列满时丢弃最旧的结果
                        try:
                            self._result_queue.get_nowait()
                            self._result_queue.put_nowait(result)
                        except queue.Empty:
                            pass
                    
                    # 调用回调函数
                    self._call_result_callbacks(result)
                    
                    # 更新统计
                    self._update_stats(result.processing_time)
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"检测处理循环异常: {e}")
        
        self.logger.info("检测处理线程结束")
    
    def _process_detection(self, frame_data: FrameData) -> Optional[DetectionResult]:
        """处理单帧检测"""
        start_time = time.time()
        
        try:
            # 执行检测
            results = self.model.predict(
                frame_data.image,
                conf=self.config.confidence_threshold,
                iou=self.config.iou_threshold,
                max_det=self.config.max_detections,
                verbose=False
            )
            
            # 解析结果
            detections = []
            annotated_image = frame_data.image.copy()
            
            if results and len(results) > 0:
                result = results[0]
                
                # 绘制检测结果
                annotated_image = result.plot()
                
                # 提取检测信息
                if result.boxes is not None:
                    for box in result.boxes:
                        detection = self._extract_detection(box, result.names)
                        if detection:
                            detections.append(detection)
                            self._draw_additional_info(annotated_image, detection)
            
            processing_time = time.time() - start_time
            
            return DetectionResult(
                frame_id=frame_data.frame_id,
                timestamp=frame_data.timestamp,
                detections=detections,
                processing_time=processing_time,
                annotated_image=annotated_image
            )
            
        except Exception as e:
            self.logger.error(f"检测处理失败: {e}")
            return None
    
    def _extract_detection(self, box, names) -> Optional[Detection]:
        """提取检测信息"""
        try:
            x1, y1, x2, y2 = map(int, box.xyxy[0])
            class_id = int(box.cls[0])
            label = names[class_id]
            confidence = float(box.conf[0])
            
            cx = (x1 + x2) // 2
            cy = (y1 + y2) // 2
            area = (x2 - x1) * (y2 - y1)
            
            return Detection(
                label=label,
                confidence=confidence,
                bbox={'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2},
                center={'x': cx, 'y': cy},
                area=area,
                class_id=class_id
            )
            
        except Exception as e:
            self.logger.error(f"提取检测信息失败: {e}")
            return None
    
    def _draw_additional_info(self, image: np.ndarray, detection: Detection):
        """绘制附加信息"""
        try:
            bbox = detection.bbox
            center = detection.center
            
            # 绘制中心点
            cv2.circle(image, (center['x'], center['y']), 5, (0, 0, 255), -1)
            
            # 绘制中心点坐标
            center_text = f"({center['x']}, {center['y']})"
            cv2.putText(image, center_text, (center['x'] + 5, center['y'] + 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
            
            # 绘制四个角点信息
            self._draw_corner_info(image, bbox)
            
        except Exception as e:
            self.logger.error(f"绘制附加信息失败: {e}")
    
    def _draw_corner_info(self, image: np.ndarray, bbox: Dict[str, int]):
        """绘制角点信息"""
        h, w = image.shape[:2]
        font_scale = 0.4
        thickness = 1
        
        # 左上角
        lt_text = f"LT({bbox['x1']}, {bbox['y1']})"
        lt_pos = (max(bbox['x1'] + 5, 5), max(bbox['y1'] + 15, 15))
        cv2.putText(image, lt_text, lt_pos, cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 255, 0), thickness)
        
        # 右上角
        rt_text = f"RT({bbox['x2']}, {bbox['y1']})"
        text_size = cv2.getTextSize(rt_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)[0]
        rt_pos = (min(bbox['x2'] - text_size[0] - 5, w - text_size[0] - 5), max(bbox['y1'] - 5, 15))
        cv2.putText(image, rt_text, rt_pos, cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 255, 0), thickness)
        
        # 左下角
        lb_text = f"LB({bbox['x1']}, {bbox['y2']})"
        lb_pos = (max(bbox['x1'] + 5, 5), min(bbox['y2'] + 15, h - 5))
        cv2.putText(image, lb_text, lb_pos, cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 255, 0), thickness)
        
        # 右下角
        rb_text = f"RB({bbox['x2']}, {bbox['y2']})"
        text_size = cv2.getTextSize(rb_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)[0]
        rb_pos = (min(bbox['x2'] - text_size[0] - 5, w - text_size[0] - 5), min(bbox['y2'] + 15, h - 5))
        cv2.putText(image, rb_text, rb_pos, cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 255, 0), thickness)
    
    def process_frame(self, frame_data: FrameData) -> bool:
        """添加帧到处理队列"""
        try:
            self._input_queue.put_nowait(frame_data)
            return True
        except queue.Full:
            # 队列满时丢弃最旧的帧
            try:
                self._input_queue.get_nowait()
                self._input_queue.put_nowait(frame_data)
                return True
            except queue.Empty:
                return False
    
    def get_latest_result(self, timeout: float = 0.1) -> Optional[DetectionResult]:
        """获取最新检测结果"""
        try:
            return self._result_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def add_result_callback(self, callback: Callable[[DetectionResult], None]):
        """添加结果回调"""
        self._result_callbacks.append(callback)
    
    def remove_result_callback(self, callback: Callable[[DetectionResult], None]):
        """移除结果回调"""
        if callback in self._result_callbacks:
            self._result_callbacks.remove(callback)
    
    def _call_result_callbacks(self, result: DetectionResult):
        """调用结果回调"""
        for callback in self._result_callbacks:
            try:
                callback(result)
            except Exception as e:
                self.logger.error(f"结果回调执行失败: {e}")
    
    def _update_stats(self, processing_time: float):
        """更新统计信息"""
        self._total_processed += 1
        self._total_processing_time += processing_time
        
        current_time = time.time()
        if current_time - self._last_stats_time >= 10.0:  # 每10秒记录一次统计
            avg_time = self._total_processing_time / self._total_processed if self._total_processed > 0 else 0
            fps = self._total_processed / (current_time - self._last_stats_time)
            
            self.logger.info(f"检测统计 - 处理帧数: {self._total_processed}, "
                           f"平均处理时间: {avg_time:.3f}s, FPS: {fps:.1f}")
            
            self._total_processed = 0
            self._total_processing_time = 0.0
            self._last_stats_time = current_time
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'total_processed': self._total_processed,
            'average_processing_time': self._total_processing_time / max(self._total_processed, 1),
            'queue_size': self._input_queue.qsize(),
            'result_queue_size': self._result_queue.qsize()
        }
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("开始清理检测处理器资源")
        self.stop_processing()
        
        with self._lock:
            self.model = None
            self.is_loaded = False
        
        self.logger.info("检测处理器资源清理完成")
