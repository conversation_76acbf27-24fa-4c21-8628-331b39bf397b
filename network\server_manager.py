# network/server_manager.py
import socket
import threading
import json
import time
from typing import Dict, Any, List, Callable, Optional
from dataclasses import dataclass, asdict
import queue

from utils.exceptions import NetworkException
from utils.error_handler import exception_handler, <PERSON>rror<PERSON>andler
from utils.logger import Logger<PERSON>anager
from detection.detection_processor import DetectionResult

@dataclass
class ServerConfig:
    """服务器配置"""
    host: str = "127.0.0.1"
    port: int = 8868
    max_connections: int = 5
    timeout: int = 30
    buffer_size: int = 4096

@dataclass
class ClientInfo:
    """客户端信息"""
    address: tuple
    connect_time: float
    last_activity: float
    socket: socket.socket

class Protocol:
    """通信协议"""
    
    # 消息类型
    MSG_HEARTBEAT = "heartbeat"
    MSG_DETECTION_RESULT = "detection_result"
    MSG_CAMERA_STATUS = "camera_status"
    MSG_SYSTEM_STATUS = "system_status"
    MSG_COMMAND = "command"
    MSG_RESPONSE = "response"
    MSG_ERROR = "error"
    
    @staticmethod
    def create_message(msg_type: str, data: Any = None) -> str:
        """创建消息"""
        message = {
            "type": msg_type,
            "timestamp": time.time(),
            "data": data
        }
        return json.dumps(message, ensure_ascii=False)
    
    @staticmethod
    def parse_message(message: str) -> Dict[str, Any]:
        """解析消息"""
        try:
            return json.loads(message)
        except json.JSONDecodeError:
            raise NetworkException(f"无效的JSON消息: {message}")

class ServerManager:
    """网络服务器管理器"""
    
    def __init__(self, config: ServerConfig, logger_name: str = 'server'):
        self.config = config
        self.logger = LoggerManager().get_logger(logger_name)
        self.error_handler = ErrorHandler(self.logger)
        
        # 服务器相关
        self.server_socket = None
        self.is_running = False
        
        # 线程管理
        self._server_thread = None
        self._client_threads = {}
        self._stop_event = threading.Event()
        
        # 客户端管理
        self._clients = {}  # socket -> ClientInfo
        self._clients_lock = threading.RLock()
        
        # 消息队列
        self._broadcast_queue = queue.Queue()
        
        # 回调函数
        self._message_handlers = {}
        self._client_handlers = {
            'connect': [],
            'disconnect': []
        }
        
        # 注册默认消息处理器
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """注册默认消息处理器"""
        self.register_message_handler(Protocol.MSG_HEARTBEAT, self._handle_heartbeat)
        self.register_message_handler(Protocol.MSG_COMMAND, self._handle_command)
    
    @exception_handler('server', default_return=False)
    def start(self) -> bool:
        """启动服务器"""
        if self.is_running:
            self.logger.warning("服务器已在运行")
            return True
        
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.settimeout(1.0)
            self.server_socket.bind((self.config.host, self.config.port))
            self.server_socket.listen(self.config.max_connections)
            
            self.is_running = True
            self._stop_event.clear()
            
            # 启动服务器线程
            self._server_thread = threading.Thread(
                target=self._server_loop,
                name="ServerManager",
                daemon=True
            )
            self._server_thread.start()
            
            self.logger.info(f"服务器启动成功 - {self.config.host}:{self.config.port}")
            return True
            
        except Exception as e:
            raise NetworkException(f"启动服务器失败: {e}")
    
    def stop(self):
        """停止服务器"""
        if not self.is_running:
            return
        
        self.logger.info("停止服务器")
        self.is_running = False
        self._stop_event.set()
        
        # 关闭所有客户端连接
        with self._clients_lock:
            for client_socket in list(self._clients.keys()):
                self._disconnect_client(client_socket)
        
        # 等待服务器线程结束
        if self._server_thread and self._server_thread.is_alive():
            self._server_thread.join(timeout=2.0)
        
        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except Exception as e:
                self.logger.error(f"关闭服务器socket失败: {e}")
            self.server_socket = None
        
        self.logger.info("服务器已停止")
    
    def _server_loop(self):
        """服务器主循环"""
        self.logger.info("服务器主线程启动")
        
        while not self._stop_event.is_set():
            try:
                client_socket, address = self.server_socket.accept()
                
                # 检查连接数限制
                with self._clients_lock:
                    if len(self._clients) >= self.config.max_connections:
                        self.logger.warning(f"达到最大连接数限制，拒绝连接: {address}")
                        client_socket.close()
                        continue
                
                # 创建客户端信息
                client_info = ClientInfo(
                    address=address,
                    connect_time=time.time(),
                    last_activity=time.time(),
                    socket=client_socket
                )
                
                # 添加到客户端列表
                with self._clients_lock:
                    self._clients[client_socket] = client_info
                
                # 启动客户端处理线程
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_socket,),
                    name=f"Client-{address[0]}:{address[1]}",
                    daemon=True
                )
                client_thread.start()
                self._client_threads[client_socket] = client_thread
                
                self.logger.info(f"客户端连接: {address}")
                self._call_client_handlers('connect', client_info)
                
            except socket.timeout:
                continue
            except Exception as e:
                if self.is_running:
                    self.logger.error(f"接受客户端连接失败: {e}")
        
        self.logger.info("服务器主线程结束")
    
    def _handle_client(self, client_socket: socket.socket):
        """处理客户端连接"""
        client_info = self._clients.get(client_socket)
        if not client_info:
            return
        
        address = client_info.address
        self.logger.info(f"开始处理客户端: {address}")
        
        try:
            client_socket.settimeout(self.config.timeout)
            
            while not self._stop_event.is_set() and self.is_running:
                try:
                    # 接收数据
                    data = client_socket.recv(self.config.buffer_size)
                    if not data:
                        break
                    
                    # 更新活动时间
                    client_info.last_activity = time.time()
                    
                    # 解析消息
                    message_str = data.decode('utf-8')
                    self.logger.debug(f"收到来自 {address} 的消息: {message_str}")
                    
                    try:
                        message = Protocol.parse_message(message_str)
                        self._process_message(client_socket, message)
                    except NetworkException as e:
                        self.logger.error(f"解析消息失败: {e}")
                        error_response = Protocol.create_message(
                            Protocol.MSG_ERROR,
                            {"error": str(e)}
                        )
                        self._send_to_client(client_socket, error_response)
                    
                except socket.timeout:
                    # 检查心跳超时
                    if time.time() - client_info.last_activity > self.config.timeout:
                        self.logger.warning(f"客户端 {address} 心跳超时")
                        break
                    continue
                except Exception as e:
                    self.logger.error(f"处理客户端 {address} 数据失败: {e}")
                    break
        
        except Exception as e:
            self.logger.error(f"客户端 {address} 处理异常: {e}")
        finally:
            self._disconnect_client(client_socket)
    
    def _disconnect_client(self, client_socket: socket.socket):
        """断开客户端连接"""
        with self._clients_lock:
            client_info = self._clients.pop(client_socket, None)
            if client_info:
                self.logger.info(f"客户端断开: {client_info.address}")
                self._call_client_handlers('disconnect', client_info)
        
        # 关闭socket
        try:
            client_socket.close()
        except Exception as e:
            self.logger.error(f"关闭客户端socket失败: {e}")
        
        # 清理线程引用
        self._client_threads.pop(client_socket, None)
    
    def _process_message(self, client_socket: socket.socket, message: Dict[str, Any]):
        """处理消息"""
        msg_type = message.get('type')
        if msg_type in self._message_handlers:
            try:
                self._message_handlers[msg_type](client_socket, message)
            except Exception as e:
                self.logger.error(f"处理消息 {msg_type} 失败: {e}")
        else:
            self.logger.warning(f"未知消息类型: {msg_type}")
    
    def _handle_heartbeat(self, client_socket: socket.socket, message: Dict[str, Any]):
        """处理心跳消息"""
        response = Protocol.create_message(Protocol.MSG_HEARTBEAT, {"status": "ok"})
        self._send_to_client(client_socket, response)
    
    def _handle_command(self, client_socket: socket.socket, message: Dict[str, Any]):
        """处理命令消息"""
        command = message.get('data', {}).get('command')
        self.logger.info(f"收到命令: {command}")
        
        # 这里可以根据具体需求处理不同的命令
        response_data = {"command": command, "status": "received"}
        response = Protocol.create_message(Protocol.MSG_RESPONSE, response_data)
        self._send_to_client(client_socket, response)
    
    def _send_to_client(self, client_socket: socket.socket, message: str) -> bool:
        """发送消息到客户端"""
        try:
            client_socket.send(message.encode('utf-8'))
            return True
        except Exception as e:
            self.logger.error(f"发送消息到客户端失败: {e}")
            return False
    
    def broadcast_message(self, msg_type: str, data: Any = None):
        """广播消息到所有客户端"""
        message = Protocol.create_message(msg_type, data)
        
        with self._clients_lock:
            disconnected_clients = []
            for client_socket in self._clients:
                if not self._send_to_client(client_socket, message):
                    disconnected_clients.append(client_socket)
            
            # 清理断开的客户端
            for client_socket in disconnected_clients:
                self._disconnect_client(client_socket)
    
    def send_detection_result(self, result: DetectionResult):
        """发送检测结果"""
        # 转换检测结果为可序列化的格式
        data = {
            'frame_id': result.frame_id,
            'timestamp': result.timestamp,
            'processing_time': result.processing_time,
            'detections': [asdict(detection) for detection in result.detections]
        }
        self.broadcast_message(Protocol.MSG_DETECTION_RESULT, data)
    
    def send_camera_status(self, status: Dict[str, Any]):
        """发送相机状态"""
        self.broadcast_message(Protocol.MSG_CAMERA_STATUS, status)
    
    def send_system_status(self, status: Dict[str, Any]):
        """发送系统状态"""
        self.broadcast_message(Protocol.MSG_SYSTEM_STATUS, status)
    
    def register_message_handler(self, msg_type: str, handler: Callable):
        """注册消息处理器"""
        self._message_handlers[msg_type] = handler
    
    def register_client_handler(self, event_type: str, handler: Callable):
        """注册客户端事件处理器"""
        if event_type in self._client_handlers:
            self._client_handlers[event_type].append(handler)
    
    def _call_client_handlers(self, event_type: str, client_info: ClientInfo):
        """调用客户端事件处理器"""
        for handler in self._client_handlers.get(event_type, []):
            try:
                handler(client_info)
            except Exception as e:
                self.logger.error(f"客户端事件处理器执行失败: {e}")
    
    def get_client_count(self) -> int:
        """获取客户端连接数"""
        with self._clients_lock:
            return len(self._clients)
    
    def get_client_info(self) -> List[Dict[str, Any]]:
        """获取客户端信息"""
        with self._clients_lock:
            return [
                {
                    'address': client_info.address,
                    'connect_time': client_info.connect_time,
                    'last_activity': client_info.last_activity,
                    'connected_duration': time.time() - client_info.connect_time
                }
                for client_info in self._clients.values()
            ]
